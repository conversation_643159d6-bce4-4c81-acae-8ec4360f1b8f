name: dev-dwv-admin-central-dashboard
on:
    workflow_dispatch:
    # schedule:
    #   # Runs every day at 4:00 AM UTC, which is 9:30 AM IST
    #   - cron: '0 4 * * *'

env:
  ECR_REPOSITORY: dwv-shyftlabs-dev-ecr-dwv-admin-dashboard
  ECR_REGISTRY: 611263743042.dkr.ecr.ca-central-1.amazonaws.com

jobs:
    build:
        name: dev-dwv-admin-dashboard-build
        runs-on: glb-shyftlabs-dev-ec2-cicd

        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: Build, tag, and push the image to Amazon ECR
              id: build-image
              env:
                  IMAGE_TAG: ${{ github.run_number }}

              run: |
                  # Build a docker container and push it to ECR 
                  aws ecr get-login-password --region ca-central-1 | sudo docker login --username AWS --password-stdin $ECR_REGISTRY
                  sudo docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG --build-arg VITE_PUBLIC_API_URL=${{ secrets.VITE_PUBLIC_API_URL_DEV }} .
                  sudo echo "Pushing image to ECR..."
                  sudo docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
    deploy:
        name: dev-dwv-admin-dashboard-deploy
        runs-on: glb-shyftlabs-dev-ec2-cicd
        needs: build
        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: helm install
              id: helm
              shell: bash
              env:
                  IMAGE_TAG: ${{ github.run_number }}
              run: |
                  cd deployments/dev
                  helm upgrade --install dev-data-weaver-admin --set image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f dev-data-weaver-admin/dev-central-values.yaml dev-data-weaver-admin -n dwv-dev