server {
  listen 80;

  gzip on;
  gzip_types text/css application/javascript application/json;

  location / {
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
      expires 1y;
      add_header Cache-Control "public, immutable";
    }
    if ( $uri = '/index.html' ) {
      add_header Cache-Control 'no-store, no-cache, max-age=0';
    }
    try_files $uri $uri/ /index.html;
  }
}

