import { useEffect, useRef, useState } from "react";
import { Button, Form, Tabs } from "antd";
import { Plus } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";

import {
  deleteUserApi,
  getAllUsers,
  getPendingInvites,
  resendInvite,
  upadateStatusApiForUser,
  InviteUserInAccount,
} from "@/services/users.service";

import { getAllAccountNames } from "@/services/account.service";
import {
  pendingUsersColumns,
  userTableColumns,
} from "@/constants/tableColumns";

import { Notification } from "@/utilities/notification.utils";
import {
  COMMON,
  NOTIFICATION,
  USER_ROLES_ACCESS,
} from "@/constants/displayStrings";
import { CreateUserModal } from "@/components/CreateUserModal/CreateUserModal";
import DynamicTableComponent from "@/components/DynamicTableComponent/DynamicTableComponent";
import { createTableFetcher } from "@/utilities/commonUtils";
import { USER_ROLES, usersTabRouteMap } from "../../constants/common";

export const UserManagement = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [accountList, setAccountList] = useState([]);
  const [selectedRole, setSelectedRole] = useState(COMMON.ALL);

  const location = useLocation();
  const navigate = useNavigate();

  const defaultTab = usersTabRouteMap[location.pathname] || COMMON.ACTIVE;
  const [activeTab, setActiveTab] = useState(defaultTab);

  const page = 1;
  const pageSize = 10;

  const accountId = localStorage.getItem(COMMON.ACCOUNT_ID);

  const [form] = Form.useForm();

  const activeUsersTableRef = useRef();
  const pendingUsersTableRef = useRef();
 
  const fetchUsers = createTableFetcher(getAllUsers);
  const fetchPendingUsers = createTableFetcher(getPendingInvites);

  useEffect(() => {
    fetchAccountNames();
    if (activeTab === COMMON.ACTIVE) {
      fetchUsers(page, pageSize);
    } else if (activeTab === COMMON.PENDING) {
      fetchPendingUsers(page, pageSize);
    }
  }, [activeTab, page]);

  const fetchAccountNames = async (page = 1) => {
    try {
      const res = await getAllAccountNames(page, pageSize);
      setAccountList(res.detail || []);
    } catch (error) {
      console.error(NOTIFICATION.ACCOUNT.FAILED_TO_FETCH_ACCOUNTS, error);
    }
  };

  const handleInviteUser = async (values) => {
    const payload = {
      first_name: values.firstName,
      last_name: values.lastName,
      email: values.email,
      role_name: values.role,
      account_id: values.accountId || accountId,
    };

    try {
      const response = await InviteUserInAccount(payload);
      if (response?.status === 201) {
        Notification(
          COMMON.SUCCESS,
          NOTIFICATION.USERS.USER_INVITED,
          NOTIFICATION.USERS.USER_CREATED_INFO(values)
        );
        form.resetFields();
        setIsModalOpen(false);

        if (activeTab === COMMON.PENDING) {
          pendingUsersTableRef.current?.reload();
        } else {
          activeUsersTableRef.current?.reload();
        }
      } else {
        Notification(
          COMMON.ERROR,
          NOTIFICATION.USERS.INVITE_FAILED,
          response?.data?.detail || NOTIFICATION.USERS.INVITE_FAILED_INFO
        );
      }
    } catch (error) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.USERS.INVITE_FAILED,
        error?.response?.data?.detail || COMMON.ERROR_HAPPENED
      );
    }
  };

  const handleRoleChange = (value) => {
    setSelectedRole(value);
    if (USER_ROLES_ACCESS.includes(value)) {
      fetchAccountNames();
    }
  };

  const handleStatusToggle = async (id, newStatus) => {
    try {
      const response = await upadateStatusApiForUser(id, newStatus);

      Notification(
        COMMON.SUCCESS,
        NOTIFICATION.COMMON.STATUS_UPDATED,
        response.detail
      );
      activeUsersTableRef.current?.reload();
    } catch (error) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.COMMON.STATUS_FAILED,
        error?.response?.data?.detail || COMMON.ERROR_HAPPENED
      );
    }
  };

  const handleDeleteUser = async (record) => {
    try {
      await deleteUserApi(record.id);
      Notification(
        COMMON.SUCCESS,
        NOTIFICATION.USERS.USER_DELETED,
        NOTIFICATION.USERS.USER_DELETED_INFO(record)
      );
      if (activeTab === COMMON.PENDING) {
        pendingUsersTableRef.current?.reload();
      } else {
        activeUsersTableRef.current?.reload();
      }
    } catch (error) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.USERS.DELETE_FAILED,
        error?.response?.data?.detail || COMMON.ERROR_HAPPENED
      );
    }
  };

  const handleResendInvite = async (record) => {
    try {
      const response = await resendInvite(record.email);
      if (response?.status === 200) {
        Notification(
          COMMON.SUCCESS,
          NOTIFICATION.COMMON.INVITE_RESENT,
          NOTIFICATION.COMMON.USER_INVITE_SENT_INFO(record)
        );
      } else {
        Notification(
          COMMON.ERROR,
          NOTIFICATION.COMMON.RESENT_ERROR,
          response?.data?.detail || NOTIFICATION.COMMON.RESENT_ERROR
        );
      }
    } catch (error) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.COMMON.RESENT_ERROR,
        error?.response?.data?.detail || NOTIFICATION.COMMON.RESENT_ERROR
      );
    }
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
    navigate(usersTabRouteMap[key]);
  };

  return (
    <div className="p-2">
      <div className="bg-white rounded-lg mb-4 p-4 flex items-center justify-between">
        <div>
          <p className="text-[22px] font-medium text-[rgb(43,63,102)]">
            {COMMON.USER_MANAGEMENT}
          </p>
        </div>
        <Button
          type="primary"
          icon={<Plus size={15} />}
          onClick={() => setIsModalOpen(true)}
          style={{
            backgroundColor: "#2C3F66",
            color: "#fff",
            border: "none",
          }}
          size={COMMON.INPUT_SIZE_LARGE}
        >
          {COMMON.INVITE_USER}
        </Button>
      </div>

      <div className="bg-white p-4 rounded-lg mb-4">
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          className="custom-tabs"
          items={[
            {
              key: COMMON.ACTIVE,
              label: COMMON.ACTIVE_USERS,
              children: (
                <DynamicTableComponent
                  ref={activeUsersTableRef}
                  columns={userTableColumns(
                    handleStatusToggle,
                    handleDeleteUser,
                    accountList
                  )}
                  fetchData={fetchUsers}
                  enableSearch={true}
                  showDropDown={true}
                  dropDownValue={selectedRole}
                  dropDownOptions={USER_ROLES}
                  onDropDownChange={(value) => setSelectedRole(value)}
                />
              ),
            },
            {
              key: COMMON.PENDING,
              label: COMMON.PENDING_INVITES,
              children: (
                <DynamicTableComponent
                  ref={pendingUsersTableRef}
                  columns={pendingUsersColumns(
                    handleDeleteUser,
                    handleResendInvite,
                    accountList
                  )}
                  fetchData={fetchPendingUsers}
                  enableSearch={true}
                />
              ),
            },
          ]}
        />
      </div>

      <CreateUserModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        handleInviteUser={handleInviteUser}
        accountList={accountList}
        handleRoleChange={handleRoleChange}
        selectedRole={selectedRole}
      />
    </div>
  );
};

export default UserManagement;
