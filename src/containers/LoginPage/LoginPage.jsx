import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { login } from "@/services/auth.service";
import { Form, Input, Button, Typography, Card } from "antd";
import { Notification } from "@/utilities/notification.utils";
import {
  ADMIN_LOGIN,
  COMMON,
  NOTIFICATION,
  REQUIRED_FEILDS,
} from "../../constants/displayStrings";

const { Title, Paragraph } = Typography;

const AdminLogin = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const handleLogin = async () => {
    setError("");

    try {
      const res = await login(email, password);
      if (res.status === 200 || res.status === 201) {
        localStorage.setItem(COMMON.ACCESS_TOKEN, res.accessToken);
        localStorage.setItem(COMMON.REFRESH_TOKEN, res.refreshToken);
        localStorage.setItem(COMMON.ACCOUNT_ID, res.accountId);
        navigate("/");
      } else {
        Notification(
          COMMON.ERROR,
          NOTIFICATION.LOGIN.LOGIN_FAILED,
          res.data?.detail || NOTIFICATION.LOGIN.LOGIN_FAILED_INFO
        );
      }
    } catch (err) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.LOGIN.LOGIN_FAILED,
        err?.response?.data?.detail || NOTIFICATION.LOGIN.LOGIN_ERR_MSG
      );
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-[#f0f4f8] to-[#e2e8f0] px-4">
      <Card
        className="shadow-lg w-full max-w-md rounded-xl"
        style={{ padding: "2rem 2rem 2.5rem 2rem", borderRadius: 16 }}
      >
        <div className="text-center mb-6">
          <Title
            level={3}
            style={{ marginBottom: 4, color: "#2C3F66", fontWeight: 700 }}
          >
            {ADMIN_LOGIN.TITLE}
          </Title>
          <Paragraph type="secondary" style={{ fontSize: 14, margin: 0 }}>
            {ADMIN_LOGIN.SUBTITLE}{" "}
          </Paragraph>
        </div>

        {error && (
          <Paragraph
            type="danger"
            style={{ textAlign: "center", marginBottom: 16 }}
          >
            {error}
          </Paragraph>
        )}

        <Form layout="vertical" onFinish={handleLogin}>
          <Form.Item
            label={COMMON.EMAIL}
            name="email"
            rules={[
              { required: true, message: REQUIRED_FEILDS.EMAIL },
              { type: "email", message: REQUIRED_FEILDS.VALID_EMAIL },
            ]}
            style={{ marginBottom: 20 }}
          >
            <Input
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              size={COMMON.INPUT_SIZE_LARGE}
            />
          </Form.Item>

          <Form.Item
            label={COMMON.PASSSWORD}
            name="password"
            rules={[{ required: true, message: REQUIRED_FEILDS.PASSSWORD }]}
            style={{ marginBottom: 30 }}
          >
            <Input.Password
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              size={COMMON.INPUT_SIZE_LARGE}
            />
          </Form.Item>

          <Button
            type="primary"
            htmlType="submit"
            size={COMMON.INPUT_SIZE_LARGE}
            block
            style={{
              backgroundColor: "#2C3F66",
              borderColor: "#2C3F66",
              fontWeight: 600,
            }}
          >
            {ADMIN_LOGIN.BUTTON}
          </Button>
        </Form>
      </Card>
    </div>
  );
};

export default AdminLogin;
