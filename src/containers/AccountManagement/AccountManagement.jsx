import { useEffect, useRef, useState } from "react";
import { Button, Form, Tabs } from "antd";
import { Plus } from "lucide-react";
import {
  getAllAccounts,
  createAccount,
  upadateStatusApi,
  deleteAccountApi,
  getPendingAccounts,
} from "@/services/account.service";
import {
  accountsTableColumns,
  pendingAccountsColumns,
} from "@/constants/tableColumns";
import { USER_STATUSES } from "@/constants/common";
import { resendInvite } from "@/services/users.service";
import { Notification } from "@/utilities/notification.utils";
import { COMMON, NOTIFICATION } from "../../constants/displayStrings";
import { CreateAccountModal } from "../../components/CreateAccountModal/CreateAccountModal";
import DynamicTableComponent from "@/components/DynamicTableComponent/DynamicTableComponent";
import { createTableFetcher } from "@/utilities/commonUtils";
import { useLocation, useNavigate } from "react-router-dom";
import { accountsTabRouteMap } from "../../constants/common";

const AccountManagement = () => {
  const [filterStatus, setFilterStatus] = useState(COMMON.ALL);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();

  const defaultTab = accountsTabRouteMap[location.pathname] || COMMON.ACTIVE;
  const [activeTab, setActiveTab] = useState(defaultTab);

  const page = 1;
  const pageSize = 10;

  const [form] = Form.useForm();

  const activeAccountsTableRef = useRef();
  const pendingAccountsTableRef = useRef();

  const fetchAccounts = createTableFetcher(getAllAccounts);
  const fetchPendingAccounts = createTableFetcher(getPendingAccounts);

  useEffect(() => {
    if (activeTab === COMMON.ACTIVE) {
      fetchAccounts(page, pageSize);
    } else if (activeTab === COMMON.PENDING) {
      fetchPendingAccounts(page, pageSize);
    }
  }, [activeTab, page]);

  const handleCreateAccount = async (values) => {
    const payload = {
      name: values.accountName,
      email: values.email,
      first_name: values.firstName,
      last_name: values.lastName,
      role_name: COMMON.ADMIN,
    };

    try {
      const response = await createAccount(payload);

      if (response?.status === 201) {
        form.resetFields();
        setIsModalOpen(false);
        Notification(
          COMMON.SUCCESS,
          NOTIFICATION.ACCOUNT.ACCOUNT_CREATED,
          NOTIFICATION.ACCOUNT.ACCOUNT_CREATED_INFO(values)
        );

        if (activeTab === COMMON.PENDING) {
          pendingAccountsTableRef.current?.reload();
        } else {
          activeAccountsTableRef.current?.reload();
        }
      } else {
        Notification(
          COMMON.ERROR,
          NOTIFICATION.ACCOUNT.ACCOUNT_ERROR,
          response?.data?.detail ||
            NOTIFICATION.ACCOUNT.FAILED_TO_CREATE_ACCOUNT
        );
      }
    } catch (error) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.ACCOUNT.ACCOUNT_ERROR,
        response?.data?.detail || NOTIFICATION.ACCOUNT.FAILED_TO_CREATE_ACCOUNT
      );
    }
  };

  const handleStatusToggle = async (id, newStatus) => {
    try {
      await upadateStatusApi(id, newStatus);
      Notification(
        COMMON.SUCCESS,
        NOTIFICATION.COMMON.STATUS_UPDATED,
        NOTIFICATION.ACCOUNT.STATUS_UPDATED_INFO(newStatus)
      );
      activeAccountsTableRef.current?.reload();
    } catch (error) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.COMMON.STATUS_FAILED,
        error?.response?.data?.detail || NOTIFICATION.ACCOUNT.ERROR_HAPPENED
      );
    }
  };

  const handleDeleteAccount = async (record) => {
    try {
      await deleteAccountApi(record.id);
      Notification(
        COMMON.SUCCESS,
        NOTIFICATION.ACCOUNT.ACCOUNT_DELETED,
        NOTIFICATION.ACCOUNT.ACCOUNT_DELETED_INFO(record)
      );
      if (activeTab === COMMON.PENDING) {
        pendingAccountsTableRef.current?.reload();
      } else {
        activeAccountsTableRef.current?.reload();
      }
    } catch (error) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.ACCOUNT.DELETE_FAILED,
        error?.response?.data?.detail || NOTIFICATION.ACCOUNT.ERROR_HAPPENED
      );
    }
  };

  const handleResendInvite = async (record) => {
    try {
      const response = await resendInvite(record.admin_email);
      if (response?.status === 200) {
        Notification(
          COMMON.SUCCESS,
          NOTIFICATION.COMMON.INVITE_RESENT,
          NOTIFICATION.COMMON.INVITE_SENT_INFO(record)
        );
      } else {
        Notification(
          COMMON.ERROR,
          NOTIFICATION.COMMON.RESENT_ERROR,
          response?.data?.detail || NOTIFICATION.COMMON.RESENT_FAILED
        );
      }
    } catch (error) {
      Notification(
        COMMON.ERROR,
        NOTIFICATION.COMMON.RESENT_ERROR,
        error?.response?.data?.detail || NOTIFICATION.COMMON.RESENT_FAILED
      );
    }
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
    navigate(accountsTabRouteMap[key]);
  };

  return (
    <div className="p-2">
      <div className="bg-white rounded-lg mb-4 p-4 flex items-center justify-between">
        <p className="text-[22px] font-medium text-[rgb(43,63,102)]">
          {COMMON.ACCOUNT_MANAGEMENT}
        </p>

        <Button
          type="primary"
          icon={<Plus size={15} />}
          onClick={() => setIsModalOpen(true)}
          style={{
            backgroundColor: "#2C3F66",
            color: "#fff",
            border: "none",
          }}
          size="large"
        >
          {COMMON.CREATE_ACCOUNT}
        </Button>
      </div>

      <div className="bg-white p-4 rounded-lg mb-4">
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          className="custom-tabs"
          items={[
            {
              key: COMMON.ACTIVE,
              label: COMMON.ACTIVE_ACCOUNTS,
              children: (
                <>
                  <DynamicTableComponent
                    ref={activeAccountsTableRef}
                    columns={accountsTableColumns(
                      handleStatusToggle,
                      handleDeleteAccount
                    )}
                    fetchData={fetchAccounts}
                    enableSearch={true}
                    showDropDown={true}
                    dropDownValue={filterStatus}
                    dropDownOptions={USER_STATUSES}
                    onDropDownChange={(value) => setFilterStatus(value)}
                  />
                </>
              ),
            },
            {
              key: COMMON.PENDING,
              label: COMMON.PENDING_ACCOUNTS,
              children: (
                <>
                  <DynamicTableComponent
                    ref={pendingAccountsTableRef}
                    columns={pendingAccountsColumns(
                      handleDeleteAccount,
                      handleResendInvite
                    )}
                    fetchData={fetchPendingAccounts}
                    enableSearch={true}
                  />
                </>
              ),
            },
          ]}
        />
      </div>

      <CreateAccountModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        handleCreateAccount={handleCreateAccount}
      />
    </div>
  );
};

export default AccountManagement;
