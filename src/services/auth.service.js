import axios from "@/configs/axios.config";

export const login = async (email = '', password = '') => {
    try {
        const res = await axios.post("/super-admin/login", {
          email,
          password,
        });

        const loginDetails = {
            accessToken: res.data.detail.access_token,
            refreshToken: res.data.detail.refresh_token,
            active: res.data.detail.active,
            createdAt: res.data.detail.created_at,
            firstLoginFlag: res.data.detail.first_login_flag,
            email: res.data.detail.email,
            firstName: res.data.detail.first_name,
            accountId: res.data.detail.account_id,
            id: res.data.detail._id,
            invitePending: res.data.detail.invite_pending,
            lastName: res.data.detail.last_name,
            roleName: res.data.detail.role_name,
            updatedAt: res.data.detail.updated_at,
            status: 200 || 201,
            dataExists: res.data.detail.data_exists,
        };

        return loginDetails;
    } catch (err) {
        console.log('Error - auth service - ', err);
        return err.response;
    }
};

export const refreshSession = async (token) => {
  try {
    const res = await axios.get("/user/refresh", {
      params: {
        refresh_token: token,
      },
    });

    const sessionDetails = {
      accessToken: res.data.detail.access_token,
      refreshToken: res.data.detail.refresh_token,
    };
    return sessionDetails;
  } catch (err) {
    console.log("Error - Auth Service - Error while refresh session", err);
    throw new Error("Unable to refresh the session.");
  }
};
