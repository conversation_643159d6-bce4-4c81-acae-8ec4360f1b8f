import axios from "@/configs/axios.config";

export const getAllAccounts = async (
  page,
  pageSize,
  filters = {},
  sortPayload = {},
  searchInput
) => {
  try {
    const params = {
      page,
      page_size: pageSize,
    };

    if (searchInput) {
      params.search_param = searchInput;
    }

    if (filters.account?.length > 0) {
      params.account_name = `in:[${filters.account.join(",")}]`;
    }

    if (sortPayload.field) {
      params.sort_param = `${sortPayload.field}:${
        sortPayload.order === "ascend" ? "asc" : "desc"
      }`;
    }
    const response = await axios.get(`/account/all`, { params });
    return response.data;
  } catch (error) {
    console.error("Error fetching accounts:", error);
    throw error;
  }
};

export const createAccount = async (payload) => {
  try {
    const response = await axios.post(`/account/create`, payload);
    return response;
  } catch (error) {
    console.error("Error creating account:", error);
    return error.response;
  }
};

export const getAllAccountNames = async (page, pageSize) => {
  try {
    const response = await axios.get(`/account/names`, {
      params: {
        page: page,
        page_size: pageSize,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching accounts:", error);
    return error.response;
  }
};

export const upadateStatusApi = async (accountId, status) => {
  try {
    const response = await axios.put(`/account/${accountId}/edit-status`, {
      active: status,
    });
    return response.data;
  } catch (error) {
    console.error("Error updating account status:", error);
    throw error;
  }
};

export const deleteAccountApi = async (accountId) => {
  try {
    const response = await axios.post(`/account/delete`, {
      id: accountId,
    });
    return response.data;
  } catch (error) {
    console.error("Error updating account status:", error);
    throw error;
  }
};

export const getPendingAccounts = async (
  page,
  pageSize,
  filters = {},
  sortPayload = {},
  searchInput
) => {
  try {
    const params = {
      page,
      page_size: pageSize || 10,
    };

    if (searchInput) {
      params.search_param = searchInput;
    }

    if (filters.account?.length > 0) {
      params.account_name = `in:[${filters.account.join(",")}]`;
    }
    if (sortPayload.field) {
      params.sort_param = `${sortPayload.field}:${
        sortPayload.order === "ascend" ? "asc" : "desc"
      }`;
    }
    const response = await axios.get(`/account/pending-invites`, { params });
    return response.data;
  } catch (error) {
    console.error("Error fetching pending accounts:", error);
    throw error;
  }
};