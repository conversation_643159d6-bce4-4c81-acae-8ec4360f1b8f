import axios from "@/configs/axios.config";

export const getAllUsers = async (
  page,
  pageSize,
  filters = {},
  sortPayload = {},
  searchInput
) => {
  try {
    const params = {
      page,
      page_size: pageSize,
    };

    if (searchInput) {
      params.search_param = searchInput;
    }

    if (filters.account?.length > 0) {
      params.account_name = `in:[${filters.account.join(",")}]`;
    }

    if (sortPayload.field) {
      params.sort_param = `${sortPayload.field}:${
        sortPayload.order === "ascend" ? "asc" : "desc"
      }`;
    }
    const response = await axios.get(`/super-admin/all-users`, { params });
    return response.data;
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
};

export const InviteUserInAccount = async (payload) => {
  try {
    const response = await axios.post(`/super-admin/invite-user`, payload);
    return response;
  } catch (error) {
    console.error("Error creating account:", error);
    return error.response;
  }
};

export const upadateStatusApiForUser = async (accountId, status) => {
  try {
    const response = await axios.put(`/user/${accountId}/edit-status`, {
      active: status,
    });
    return response.data;
  } catch (error) {
    console.error("Error updating account status:", error);
    throw error;
  }
};

export const deleteUserApi = async (userId) => {
  try {
    const response = await axios.get(`/user/${userId}/delete`, {
      id: userId,
    });
    return response.data;
  } catch (error) {
    console.error("Error deleting user:", error);
    throw error;
  }
};

export const getPendingInvites = async (
  page,
  pageSize,
  filters = {},
  sortPayload = {},
  searchInput
) => {
  try {
    const params = {
      page,
      page_size: pageSize || 10,
    };

    if (searchInput) {
      params.search_param = searchInput;
    }

    if (filters.account?.length > 0) {
      params.account_name = `in:[${filters.account.join(",")}]`;
    }
    if (sortPayload.field) {
      params.sort_param = `${sortPayload.field}:${
        sortPayload.order === "ascend" ? "asc" : "desc"
      }`;
    }
    const response = await axios.get(`/super-admin/pending-invites`, {
      params,
    });
    return response.data.detail;
  } catch (error) {
    console.error("Error fetching pending invites:", error);
    throw error;
  }
};

export const resendInvite = async (email) => {
  try {
    const response = await axios.post(`/user/resend-password-link`, {
      email: email,
    });
    return response;
  } catch (error) {
    console.error("Error resending invite:", error);
    throw error;
  }
};
