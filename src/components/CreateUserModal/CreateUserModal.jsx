import { Form, Input, Modal, Select } from "antd";
import { ALL_USER_ROLES } from "@/constants/common";
import {
  COMMON,
  PLACEHOLDERS,
  REQUIRED_FEILDS,
  USER_ROLES_ACCESS,
} from "@/constants/displayStrings";

export const CreateUserModal = ({
  isModalOpen,
  setIsModalOpen,
  handleInviteUser,
  accountList,
  handleRoleChange,
  selectedRole,
}) => {
  const [form] = Form.useForm();

  return (
    <>
      {/* Invite User Modal */}
      <Modal
        title={COMMON.INVITE_USER}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={() => form.submit()}
        okText={COMMON.INVITE_USER}
        cancelText={COMMON.CANCEL}
      >
        <Form layout="vertical" form={form} onFinish={handleInviteUser}>
          <Form.Item
            name="firstName"
            label={COMMON.FIRST_NAME}
            rules={[{ required: true, message: REQUIRED_FEILDS.FIRST_NAME }]}
          >
            <Input placeholder={PLACEHOLDERS.FIRST_NAME} />
          </Form.Item>

          <Form.Item
            name="lastName"
            label={COMMON.LAST_NAME}
            rules={[{ required: true, message: REQUIRED_FEILDS.LAST_NAME }]}
          >
            <Input placeholder={PLACEHOLDERS.LAST_NAME} />
          </Form.Item>

          <Form.Item
            name="email"
            label={COMMON.EMAIL}
            rules={[
              { required: true, message: REQUIRED_FEILDS.EMAIL },
              { type: "email", message: REQUIRED_FEILDS.VALID_EMAIL },
            ]}
          >
            <Input placeholder={PLACEHOLDERS.FIRST_NAME} />
          </Form.Item>

          <Form.Item
            name="role"
            label={COMMON.ROLE}
            rules={[{ required: true, message: REQUIRED_FEILDS.ROLE }]}
            initialValue={COMMON.SUPER_ADMIN}
          >
            <Select onChange={handleRoleChange} options={ALL_USER_ROLES} />
          </Form.Item>

          {USER_ROLES_ACCESS.includes(selectedRole) && (
            <Form.Item
              name="accountId"
              label={COMMON.SELECT_ACCOUNT}
              rules={[{ required: true, message: REQUIRED_FEILDS.ACCOUNT }]}
            >
              <Select placeholder={COMMON.SELECT_ACCOUNT}>
                {accountList.map((acc) => (
                  <Option key={acc.id} value={acc.id}>
                    {acc.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
        </Form>
      </Modal>
    </>
  );
};
