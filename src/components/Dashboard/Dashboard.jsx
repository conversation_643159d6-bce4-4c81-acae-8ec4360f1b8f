import { COMMON, DASHBOARD } from "../../constants/displayStrings";

const Dashboard = () => {
  return (
    <div className="p-2]">
      <div className="bg-white rounded-lg mb-4 p-4 flex items-center justify-between">
        <div>
          <p className="text-[22px] font-medium text-[rgb(43,63,102)]">
            {COMMON.DASHBOARD}
          </p>
        </div>
      </div>
      {/* Welcome Section */}
      <div className="w-full bg-white pb-10 pt-6 px-6 rounded-2xl shadow-sm mb-0">
        <div className="mb-4">
          <p className="text-[#2b3f66] text-[22px] font-medium leading-tight">
            {DASHBOARD.TITTLE}
          </p>
          <p className="text-gray-600 text-base mt-1">{DASHBOARD.SUB_TITLE}</p>
        </div>

        {/* Quick Actions Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-[#f9fafb] p-4 rounded-lg border hover:shadow transition">
            <h3 className="text-[#2b3f66] font-semibold mb-2">
              {DASHBOARD.MANAGE_ACCOUNTS}
            </h3>
            <p className="text-sm text-gray-500">
              {DASHBOARD.MANAGE_ACCOUNTS_CONTENT}{" "}
            </p>
          </div>

          <div className="bg-[#f9fafb] p-4 rounded-lg border hover:shadow transition">
            <h3 className="text-[#2b3f66] font-semibold mb-2">
              {DASHBOARD.USER_ACCESS}
            </h3>
            <p className="text-sm text-gray-500">
              {DASHBOARD.USER_ACCESS_CONTENT}{" "}
            </p>
          </div>

          <div className="bg-[#f9fafb] p-4 rounded-lg border hover:shadow transition">
            <h3 className="text-[#2b3f66] font-semibold mb-2">
              {DASHBOARD.SYSTEM_LOG}
            </h3>
            <p className="text-sm text-gray-500">
              {DASHBOARD.SYSTEM_LOG_CONTENT}{" "}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
