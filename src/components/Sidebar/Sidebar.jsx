import { Layout, <PERSON>u, <PERSON><PERSON>, Image } from "antd";
import { LayoutGrid, Landmark, Bell, UserRound, Power } from "lucide-react";
import logo from "../../../public/logoInitial.svg";

const { Sider } = Layout;

const Sidebar = ({ currentView, onViewChange, onLogout }) => {
  const navigation = [
    {
      key: "dashboard",
      label: "Dashboard",
      icon: <LayoutGrid size={18} className="sidebar-icon-style" />,
    },
    {
      key: "accounts",
      label: "Account Management",
      icon: <Landmark size={18} className="sidebar-icon-style" />,
    },
    {
      key: "users",
      label: "User Management",
      icon: <UserRound size={18} className="sidebar-icon-style" />,
    },
    {
      key: "logs",
      label: "System Logs",
      icon: <Bell size={18} className="sidebar-icon-style" />,
    },
  ];

  return (
    <div className="sidebar">
      <Sider
        width={250}
        className="!bg-white !fixed h-screen !p-0 overflow-auto"
      >
        <div className="h-full flex flex-col">
          {/* Top Section */}
          <div>
            <div className="p-5 flex items-center gap-3">
              <div className="bg-[#f0f4f9] h-12 w-12 rounded-lg flex items-center justify-center">
                <span className="text-[#2C3F66] text-sm font-bold tracking-wide">
                  <Image
                    height={22}
                    width={22}
                    src={logo}
                    alt="DataWeaver Logo"
                    preview={false}
                  />
                </span>
              </div>
              <div>
                <h1 className="text-[#2C3F66] text-[18px] font-bold tracking-widest m-0">
                  DataWeaver
                </h1>
                <p className="text-[#2f4b73] text-xs m-0">Super Admin</p>
              </div>
            </div>

            <Menu
              mode="inline"
              selectedKeys={[currentView]}
              onClick={({ key }) => onViewChange(key)}
              items={navigation}
            />
          </div>

          <div className="mt-auto p-5 border-t border-gray-200">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-[#2C3F66] font-bold">
                SA
              </div>
              <div>
                <p className="text-sm text-[#2C3F66] font-semibold mb-0">
                  ShyftLabs Admin
                </p>
                <p className="text-xs text-gray-500 mb-0">SUPER ADMIN</p>
              </div>
            </div>
            <Button
              onClick={onLogout}
              icon={<Power size={15}/>}
              block
              style={{
                color: "#2C3F66",
                borderRadius: "10px",
                fontWeight: "500",
              }}
            >
              Logout
            </Button>
          </div>
        </div>
      </Sider>
    </div>
  );
};

export default Sidebar;
