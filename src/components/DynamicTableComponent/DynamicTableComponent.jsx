import { useEffect, useState, useImperativeHandle, forwardRef } from "react";
import { Table, Input, Select } from "antd";
import { Search } from "lucide-react";
import { useSearchParams } from "react-router-dom";
import { COMMON } from "@/constants/displayStrings";

const DynamicTableComponent = forwardRef(
  (
    {
      columns,
      fetchData,
      initialParams = {},
      pageSize = 10,
      enableSearch = false,
      showDropDown = false,
      dropDownValue,
      dropDownOptions,
      onDropDownChange,
    },
    ref
  ) => {
    const [loading, setLoading] = useState(false);
    const [dataSource, setDataSource] = useState([]);
    const [pagination, setPagination] = useState({ current: 1, pageSize });
    const [filters, setFilters] = useState({});
    const [sorter, setSorter] = useState({});
    const [searchParams, setSearchParams] = useSearchParams();
    const [searchInput, setSearchInput] = useState(
      searchParams.get("search") || ""
    );

    const fetchTableData = async () => {
      setLoading(true);
      try {
        const data = await fetchData({
          pagination,
          filters,
          sorter,
          search: searchInput,
          ...initialParams,
        });

        setDataSource(data?.detail?.data || data?.data || []);
        setPagination((prev) => ({
          ...prev,
          total: data?.total || 0,
        }));
      } catch (err) {
        console.error("Failed to fetch table data:", err);
        setDataSource([]);
      } finally {
        setLoading(false);
      }
    };

    useImperativeHandle(ref, () => ({
      reload: () => {
        fetchTableData();
      },
    }));

    useEffect(() => {
      fetchTableData();
    }, [pagination.current, pagination.pageSize, filters, sorter, searchInput]);

    const handleTableChange = (pagination, filters, sorter) => {
      const updatedSorter = {
        field: sorter.field,
        order: sorter.order,
      };

      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
      });

      setFilters(filters);
      setSorter(updatedSorter);

      const filterParams = {};
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value.length > 0) {
          filterParams[key] = value.join(",");
        }
      });

      const params = {
        page: pagination.current.toString(),
        pageSize: pagination.pageSize.toString(),
        ...filterParams,
      };

      if (updatedSorter?.field && updatedSorter?.order) {
        params.sortField = updatedSorter.field;
        params.sortOrder = updatedSorter.order;
      }

      setSearchParams(params);
    };

    const handleSearchChange = (setSearchInput, setSearchParams) => (e) => {
      const value = e.target.value;
      setSearchInput(value);

      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        if (value) {
          newParams.set("search", value);
        } else {
          newParams.delete("search");
        }
        return newParams;
      });
    };

    return (
      <>
        {(enableSearch || showDropDown) && (
          <div className="flex items-center mb-4 mt-2 flex-wrap gap-4">
            {enableSearch && (
              <Input
                prefix={<Search size={15} />}
                placeholder={COMMON.SEARCH}
                value={searchInput}
                onChange={handleSearchChange(setSearchInput, setSearchParams)}
                allowClear
                size={COMMON.INPUT_SIZE_LARGE}
                style={{ width: 500 }}
              />
            )}

            {showDropDown && (
              <Select
                value={dropDownValue}
                style={{ minWidth: 160 }}
                size={COMMON.INPUT_SIZE_LARGE}
                options={dropDownOptions}
                onChange={onDropDownChange}
              />
            )}
          </div>
        )}

        <Table
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          rowKey={(record) => `${record.id}_${record.role}`}
          pagination={pagination}
          onChange={handleTableChange}
        />
      </>
    );
  }
);

export default DynamicTableComponent;
