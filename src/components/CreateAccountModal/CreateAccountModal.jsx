import { Form, Input, Modal } from "antd";
import {
  COMMON,
  PLA<PERSON>H<PERSON>DERS,
  REQUIRED_FEILDS,
} from "../../constants/displayStrings";

export const CreateAccountModal = ({
  isModalOpen,
  setIsModalOpen,
  handleCreateAccount,
}) => {
  const [form] = Form.useForm();

  return (
    <Modal
      title={COMMON.CREATE_ACCOUNT}
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      onOk={() => form.submit()}
      okText={COMMON.CREATE_ACCOUNT}
      cancelText={COMMON.CANCEL}
    >
      <Form layout="vertical" form={form} onFinish={handleCreateAccount}>
        <Form.Item
          name="accountName"
          label={COMMON.ACCOUNT_NAME}
          rules={[{ required: true, message: REQUIRED_FEILDS.ACCOUNT_NAME }]}
        >
          <Input placeholder={PLACEHOLDERS.ACCOUNT_NAME} />
        </Form.Item>

        <Form.Item
          name="firstName"
          label={COMMON.FIRST_NAME}
          rules={[{ required: true, message: REQUIRED_FEILDS.FIRST_NAME }]}
        >
          <Input placeholder={PLACEHOLDERS.FIRST_NAME} />
        </Form.Item>

        <Form.Item
          name="lastName"
          label={COMMON.LAST_NAME}
          rules={[{ required: true, message: REQUIRED_FEILDS.LAST_NAME }]}
        >
          <Input placeholder={PLACEHOLDERS.LAST_NAME} />
        </Form.Item>

        <Form.Item
          name="email"
          label={COMMON.EMAIL}
          rules={[
            { required: true, message: REQUIRED_FEILDS.EMAIL },
            { type: "email", message: REQUIRED_FEILDS.VALID_EMAIL },
          ]}
        >
          <Input placeholder={PLACEHOLDERS.EMAIL} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
