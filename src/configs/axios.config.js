import Axios from "axios";
import { refreshSession } from "@/services/auth.service";

const axios = Axios.create({
  baseURL: import.meta.env.VITE_PUBLIC_API_URL,
  headers: {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
  },
  withCredentials: true,
});

axios.interceptors.request.use((config) => {
  config.headers["Authorization"] = `Bearer ${localStorage.getItem(
    "accessToken"
  )}`;
  return config;
});

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    console.log("error", error);
    const originalRequest = error.config;
    if (error.response.status === 401) {
      if (error.config.url === "/user/refresh") {
        localStorage.clear();
        window.location.href = "/";
        return Promise.reject(error);
      }
      //Get the refresh token
      const refreshToken = localStorage.getItem("refreshToken");
      const rememberMe = localStorage.getItem("rememberMe");
      if (refreshToken && rememberMe) {
        const res = await refreshSession(localStorage.getItem("refreshToken"));
        if (res) {
          localStorage.setItem("accessToken", res.accessToken);
          localStorage.setItem("refreshToken", res.refreshToken);
          return axios(originalRequest);
        }
        return Promise.reject(error);
      } else {
        localStorage.clear();
        window.location.href = "/";
        return Promise.reject(error);
      }
    }
    return Promise.reject(error);
  }
);

export default axios;
