export const filterAccountsBySearchAndStatus = ({
  data = [],
  searchTerm = "",
  filterStatus = "all",
  applyStatusFilter = false,
}) => {
  return data.filter((account) => {
    const nameMatch = account?.name
      ?.toLowerCase()
      ?.includes(searchTerm.toLowerCase());
    const domainMatch = account?.domain
      ?.toLowerCase()
      ?.includes(searchTerm.toLowerCase());
    const matchesSearch = nameMatch || domainMatch;

    const matchesStatus =
      !applyStatusFilter ||
      filterStatus === "all" ||
      (filterStatus === "active" && account.status === true) ||
      (filterStatus === "inactive" && account.status === false);

    return matchesSearch && matchesStatus;
  });
};

export const filterUsersBySearchAndRole = ({
  data = [],
  searchTerm = "",
  filterRole = "all",
  applyRoleFilter = false,
}) => {
  return data.filter((user) => {
    const nameMatch = user?.name
      ?.toLowerCase()
      ?.includes(searchTerm.toLowerCase());
    const emailMatch = user?.email
      ?.toLowerCase()
      ?.includes(searchTerm.toLowerCase());
    const matchesSearch =
      !searchTerm ||
      user?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user?.email?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole =
      !applyRoleFilter ||
      filterRole === "all" ||
      user.role?.toLowerCase() === filterRole.toLowerCase();

    return matchesSearch && matchesRole;
  });
};

export const createTableFetcher = (fetchApi) => {
  return async ({ pagination, filters, sorter, search }) => {
    const sortPayload = sorter?.field
      ? { field: sorter.field, order: sorter.order }
      : undefined;

    const { current = 1, pageSize = 10 } = pagination || {};

    return await fetchApi(current, pageSize, filters, sortPayload, search);
  };
};
