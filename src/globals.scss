@use "tailwindcss" as *;

.ant-menu-light .ant-menu-item{
    color: #2C3F66;
    font-weight: 400;
}
.ant-spin-dot-item {
    background-color: #2C3F66 !important;
  }
.ant-input:hover, 
.ant-input:focus, 
.ant-input-affix-wrapper:hover, 
.ant-input-affix-wrapper:focus {
  border-color: #2C3F66 !important;
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector,
.ant-select-focused .ant-select-selector {
  border-color: #2C3F66 !important;
}

.ant-btn-primary:not(:disabled):hover,
.ant-btn:not(:disabled):hover,
.ant-btn:not(:disabled):focus {
  background-color: #1e2f4d !important;
  border-color: #1e2f4d !important;
  color: #fff !important;
}

.ant-btn-primary {
  background-color: #2C3F66 !important;
  border-color: #2C3F66 !important;
  color: #fff !important;
}

.custom-tabs .ant-tabs-tab:hover,
.custom-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #2C3F66 !important;
}

.ant-tabs .ant-tabs-ink-bar {
  background-color: #2C3F66 !important;
}

.sidebar-icon-style{
  font-size: 18px !important;
  color: #2C3F66;
}