import { Navigate } from "react-router-dom";
import MainLayout from "@/components/Layout/Layout";

import Dashboard from "@/components/Dashboard/Dashboard";
import AccountManagement from "@/containers/AccountManagement/AccountManagement";
import UserManagement from "@/containers/UserManagement/UserManagement";
import SystemLogs from "@/components/SystemLogs/SystemLogs";
import Login from "@/containers/LoginPage/LoginPage";

const PrivateRoute = ({ children }) => {
  const isAuthenticated = localStorage.getItem("accessToken");
  return isAuthenticated ? children : <Navigate to="/login" />;
};

export const RoutesList = [
  {
    path: "/login",
    component: <Login />,
  },
  {
    path: "/",
    element: (
      <PrivateRoute>
        <MainLayout />
      </PrivateRoute>
    ),
    children: [
      {
        path: "",
        element: <Navigate to="/dashboard" />,
      },
      {
        path: "/dashboard",
        element: <Dashboard />,
      },
      {
        path: "/accounts",
        element: <AccountManagement />,
      },
      {
        path: "/pending-accounts",
        element: <AccountManagement />,
      },
      {
        path: "/users",
        element: <UserManagement />,
      },
      {
        path: "/pending-invites",
        element: <UserManagement />,
      },
      {
        path: "/logs",
        element: <SystemLogs />,
      },
    ],
  },
];
