import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>con<PERSON>rm, Switch } from "antd";
import dayjs from "dayjs";
import { COMMON } from "./displayStrings";
import { <PERSON><PERSON>he<PERSON>, Trash2, UserRound, RefreshCcw } from "lucide-react";

const roleBadge = {
  SUPER_ADMIN: { icon: <ShieldCheck size={12} /> },
  ADMIN: { icon: <UserRound size={12} /> },
  MANAGER: { icon: <UserRound size={12} /> },
  STAFF: { icon: <UserRound size={12} /> },
};

export const userTableColumns = (handleStatusToggle, handleDeleteUser, accountList) => [
  {
    title: "Email",
    dataIndex: "email",
    key: "email",
    sorter: true,
    render: (_, user) => <div>{user.email}</div>,
  },
  {
    title: "Role",
    dataIndex: "role",
    key: "role",
    render: (role) => {
      const { icon } = roleBadge[role] || {};
      return (
        <Tag
          style={{
            display: "inline-flex",
            alignItems: "center",
          }}
        >
          {icon}
          <span style={{ marginLeft: 6 }}>{role}</span>
        </Tag>
      );
    },
  },
  {
    title: "Account",
    dataIndex: "account_name",
    key: "account",
    filters: accountList.map((acc) => ({
      text: acc.name,
      value: acc.name,
    })),
    render: (text) => <Space>{text}</Space>,
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    render: (status, record) => (
      <Switch
        checked={status}
        onChange={(checked) => handleStatusToggle(record.id, checked)}
        disabled={record.role === COMMON.SUPER_ADMIN}
      />
    ),
  },
  {
    title: "Invited",
    dataIndex: "created",
    key: "created",
    render: (created) => dayjs(created).format("DD MMM YYYY"),
  },
  {
    title: "Actions",
    key: "actions",
    align: "right",
    render: (text, record) => (
      <Space size="middle">
        <Popconfirm
          title="Are you sure to delete this user?"
          onConfirm={() => handleDeleteUser(record)}
          okText="Yes"
          cancelText="No"
        >
          <Button
            type="text"
            icon={<Trash2 size={15} />}
            danger
            style={{
              backgroundColor: "#f0f0f0",
              borderRadius: "50%",
              padding: 8,
              color: "#2C3F66",
            }}
            disabled={record.role === COMMON.SUPER_ADMIN}
          />
        </Popconfirm>
      </Space>
    ),
  },
];

export const accountsTableColumns = (
  handleStatusToggle,
  handleDeleteAccount
) => [
  {
    title: "Account",
    dataIndex: "name",
    key: "name",
    sorter: true,
    render: (text) => (
      <div>
        <p strong>{text}</p>
      </div>
    ),
  },
  {
    title: "Admin",
    dataIndex: "admin_email",
    key: "admin_email",
    render: (_, account) => <div>{account.admin_email}</div>,
  },
  {
    title: "Users",
    dataIndex: "users_count",
    key: "users_count",
    render: (count) => (
      <Space>
        <UserRound className="w-4 h-4" />
        <span>{count}</span>
      </Space>
    ),
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    render: (status, record) => (
      <Switch
        checked={status}
        onChange={(checked) => handleStatusToggle(record.id, checked)}
      />
    ),
  },
  {
    title: "Created",
    dataIndex: "created",
    key: "created",
    render: (date) => dayjs(date).format("DD MMM YYYY"),
  },
  {
    title: "Actions",
    key: "actions",
    align: "right",
    render: (text, record) => (
      <Space size="middle">
        <Popconfirm
          title="Are you sure to delete this account?"
          onConfirm={() => handleDeleteAccount(record)}
          okText="Yes"
          cancelText="No"
        >
          <Button
            type="text"
            icon={<Trash2 size={15} />}
            danger
            style={{
              backgroundColor: "#f0f0f0",
              borderRadius: "50%",
              padding: 8,
              color: "#2C3F66",
            }}
            disabled={record.role === COMMON.SUPER_ADMIN}
          />
        </Popconfirm>
      </Space>
    ),
  },
];

export const pendingUsersColumns = (handleDeleteUser, handleResendInvite, accountList) => [
  {
    title: "Email",
    dataIndex: "email",
    key: "email",
    sorter: true,
    render: (_, user) => <div>{user.email}</div>,
  },
  {
    title: "Role",
    dataIndex: "role",
    key: "role",
    render: (role) => {
      const { icon } = roleBadge[role] || {};
      return (
        <Tag
          style={{
            display: "inline-flex",
            alignItems: "center",
          }}
        >
          {icon}
          <span style={{ marginLeft: 6 }}>{role}</span>
        </Tag>
      );
    },
  },
  {
    title: "Account",
    dataIndex: "account_name",
    key: "account",
    filters: accountList.map((acc) => ({
      text: acc.name,
      value: acc.name,
    })),
    render: (text) => <Space>{text}</Space>,
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    render: (status, record) => {
      if (record.invite_pending) {
        return <span>Pending</span>;
      }
      return null;
    },
  },
  {
    title: "Invited on",
    dataIndex: "created",
    key: "created",
    render: (created) => dayjs(created).format("DD MMM YYYY"),
  },
  {
    title: "Actions",
    key: "actions",
    align: "right",
    render: (text, record) => (
      <Space size="middle">
        <div>
          <Popconfirm
            title="Resend Invite?"
            description="Are you sure you want to resend the invite?"
            onConfirm={() => handleResendInvite(record)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={<RefreshCcw size={15} />}
              style={{
                backgroundColor: "#f0f0f0",
                borderRadius: "50%",
                padding: 8,
                color: "#2C3F66",
              }}
            />
          </Popconfirm>
        </div>

        <div>
          <Popconfirm
            title="Are you sure to delete this user?"
            onConfirm={() => handleDeleteUser(record)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={<Trash2 size={15} />}
              danger
              style={{
                backgroundColor: "#f0f0f0",
                borderRadius: "50%",
                padding: 8,
                color: "#2C3F66",
              }}
            />
          </Popconfirm>
        </div>
      </Space>
    ),
  },
];

export const pendingAccountsColumns = (
  handleDeleteAccount,
  handleResendInvite
) => [
  {
    title: "Account",
    dataIndex: "name",
    key: "name",
    sorter: true,
    render: (text) => (
      <div>
        <p strong>{text}</p>
      </div>
    ),
  },
  {
    title: "Admin",
    dataIndex: "admin_email",
    key: "admin_email",
    render: (_, account) => <div>{account.admin_email}</div>,
  },
  {
    title: "Users",
    dataIndex: "users_count",
    key: "users_count",
    render: (count) => (
      <Space>
        <UserRound className="w-4 h-4" />
        <span>{count}</span>
      </Space>
    ),
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    render: (status, record) => {
      if (record.invite_pending) {
        return <span>Pending</span>;
      }
      return null;
    },
  },
  {
    title: "Sent on",
    dataIndex: "created",
    key: "created",
    render: (date) => dayjs(date).format("DD MMM YYYY"),
  },
  {
    title: "Actions",
    key: "actions",
    align: "right",
    render: (text, record) => (
      <Space size="middle">
        <div>
          <Popconfirm
            title="Resend Invite?"
            description="Are you sure you want to resend the invite?"
            onConfirm={() => handleResendInvite(record)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={<RefreshCcw size={15} />}
              style={{
                backgroundColor: "#f0f0f0",
                borderRadius: "50%",
                padding: 8,
                color: "#2C3F66",
              }}
            />
          </Popconfirm>
        </div>
        <div>
          <Popconfirm
            title="Are you sure to delete this account?"
            onConfirm={() => handleDeleteAccount(record)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={<Trash2 size={15} />}
              danger
              style={{
                backgroundColor: "#f0f0f0",
                borderRadius: "50%",
                padding: 8,
                color: "#2C3F66",
              }}
            />
          </Popconfirm>
        </div>
      </Space>
    ),
  },
];
