export const COMMON = {
  ADMIN: "ADMIN",
  SUPER_ADMIN: "SUPER_ADMIN",

  ACTIVE: "active",

  PENDING: "pending",
  SUCCESS: "success",
  ERROR: "error",

  ACCOUNT_ID: "accountId",
  ACCESS_TOKEN: "accessToken",
  REFRESH_TOKEN: "refreshToken",

  ERROR_HAPPENED: "An error occurred.",

  ACCOUNT_MANAGEMENT: "Account Management",
  CREATE_ACCOUNT: "Create Account",
  ACTIVE_ACCOUNTS: "Active Accounts",
  PENDING_ACCOUNTS: "Pending Accounts",
  SEARCH_ACCOUNTS: "Search accounts...",
  SELECT_ACCOUNT: "Select Account",
  INPUT_SIZE_LARGE: "large",

  USER_MANAGEMENT: "User Management",
  INVITE_USER: "Invite User",
  ACTIVE_USERS: "Active Users",
  PENDING_INVITES: "Pending Invites",
  PENDING_USERS: "Pending Users",
  SEARCH_USERS: "Search users...",

  DASHBOARD: "Dashboard",
  SYSTEM_LOG: "SystemLogs",

  ALL: "all",
  ID: "id",

  CANCEL: "Cancel",

  ACCOUNT_NAME: "Account Name",
  FIRST_NAME: "First Name",
  LAST_NAME: "Last Name",
  EMAIL: "Email",
  ROLE: "Role",
  PASSSWORD: "Password",

  SEARCH: "Search..."
};

export const NOTIFICATION = {
  ACCOUNT: {
    FAILED_TO_FETCH_ACCOUNTS: "Failed to fetch accounts",
    FAILED_TO_FETCH_PENDING_ACCOUNTS: "Failed to fetch pending accounts",
    FAILED_TO_CREATE_ACCOUNT: "Failed to create account.",

    ACCOUNT_CREATED: "Account Created",
    ACCOUNT_ERROR: "Error in Creating Account",

    ACCOUNT_DELETED: "Account Deleted",
    DELETE_FAILED: "Failed to delete account",

    STATUS_UPDATED_INFO: (isActive) =>
      `Account status has been updated to ${isActive ? "Active" : "Inactive"}.`,
    ACCOUNT_CREATED_INFO: (values) =>
      `Account ${values.accountName} has been created successfully.`,
    ACCOUNT_DELETED_INFO: (record) =>
      `Account with ID ${record.admin_email} has been deleted successfully.`,
  },

  USERS: {
    FAILED_TO_FETCH_USERS: "Failed to fetch accounts",
    ERROR_FETCH_USERS: "Error fetching users",
    ERROR_FETCH_PENDING_USERS: "Error fetching pending users",

    USER_INVITED: "User Invited",
    INVITE_FAILED: "Invite Failed",
    INVITE_FAILED_INFO: "Failed to invite user.",

    USER_DELETED: "User Deleted",
    DELETE_FAILED: "Failed to delete user",
    USER_DELETED_INFO: (record) =>
      `User with account name ${record.account_name} has been deleted successfully.`,

    USER_CREATED_INFO: (values) =>
      `${values.firstName} has been invited successfully.`,
  },
  LOGIN: {
    LOGIN_FAILED: "Login Failed",
    LOGIN_FAILED_INFO: "Login attempt failed. Please try again.",
    LOGIN_ERR_MSG:
      "An error occurred during login. Please check your credentials and try again.",
  },
  COMMON: {
    STATUS_UPDATED: "Status Updated",
    STATUS_FAILED: "Failed to update status",

    INVITE_RESENT: "Invite Resent",
    RESENT_ERROR: "Error Resending Invite",
    RESENT_FAILED: "Failed to resend invite.",
    INVITE_SENT_INFO: (record) =>
      `Invite for ${record.admin_email} has been resent successfully.`,
    USER_INVITE_SENT_INFO: (record) =>
      `Invite for ${record.email} has been resent successfully.`,
  },
};

export const PLACEHOLDERS = {
  ACCOUNT_NAME: "Enter account name",
  FIRST_NAME: "Enter first name",
  LAST_NAME: "Enter last name",
  EMAIL: "Enter email",
};

export const REQUIRED_FEILDS = {
  ACCOUNT_NAME: "Please enter account name",
  EMAIL: "Please enter an email",
  FIRST_NAME: "Please enter a first name",
  LAST_NAME: "Please enter a last name",
  VALID_EMAIL: "Enter a valid email",
  ROLE: "Please select a role",
  ACCOUNT: "Please select an account",
  PASSSWORD: "Please enter your password",
};

export const ADMIN_LOGIN = {
  TITLE: "Admin Login",
  SUBTITLE: "Welcome back, here’s what’s happening with DataWeaver",
  BUTTON: "Login",
}

export const DASHBOARD = {
  TITTLE: "Welcome to the DataWeaver Admin Dashboard",
  SUB_TITLE:
    "Here you can manage accounts, invite users, and monitor activity logs.",
  MANAGE_ACCOUNTS: "Manage Accounts",
  MANAGE_ACCOUNTS_CONTENT: "Create, update or deactivate client accounts.",
  USER_ACCESS: "User Access",
  USER_ACCESS_CONTENT: "Invite users, assign roles and manage permissions.",
  SYSTEM_LOG: "System Logs",
  SYSTEM_LOG_CONTENT: "Monitor recent activity and system events."
};

export const SYSTEM_LOG = {
SYSTEM_LOG_CONTENT1: "System logs provide a real-time record of activity across the platform, helping administrators monitor actions like user sign-ins, role updates, account creations, failed login attempts, and more.",
SYSTEM_LOG_CONTENT2: " This section will populate as events occur, giving you visibility into key operational details for auditing and support.",
SYSTEM_LOG_CONTENT3: " (No logs available yet. Once users start interacting with the system, events will appear here.)"
}

export const USER_ROLES_ACCESS = ["ADMIN", "MANAGER", "STAFF"];