// src/AppRoutes.jsx
import { Routes, Route } from "react-router-dom";
import { RoutesList } from "./constants/routes";

const renderRoutes = (routes) =>
  routes.map((route, index) => {
    if (route.children) {
      return (
        <Route key={index} path={route.path} element={route.element}>
          {route.children.map((child, childIndex) => (
            <Route key={childIndex} path={child.path} element={child.element} />
          ))}
        </Route>
      );
    }

    return <Route key={index} path={route.path} element={route.component} />;
  });

const AppRouter = () => {
  return <Routes>{renderRoutes(RoutesList)}</Routes>;
};

export default AppRouter;
