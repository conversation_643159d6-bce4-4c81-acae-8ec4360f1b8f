# Default values for dev-data-weaver-admin.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

name: dwv-shyftlabs-dev-admin
namespace: dwv-dev
group: dev-admin
env: dev-admin
image: image
port: 80

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 80

ingress:
  enabled: true
  className: "alb"
  annotations: 
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/security-groups: sg-0f9f53ac1dfc7904d
    alb.ingress.kubernetes.io/subnets: subnet-06eb42215dc0f5362, subnet-0ed2735dab087e078, subnet-025aeff5cd049a0c7
    alb.ingress.kubernetes.io/group.name: dev-admin
    #alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ca-central-1:611263743042:certificate/c196aa82-610e-4b93-8ba4-93b71bffb3d5
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '40'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '30'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
  hosts:
    - host: data-weaver-admin-dev.illuminz.io
      paths:
        - path: /
          pathType: Prefix
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# autoscaling:
#   enabled: false
#   minReplicas: 1
#   maxReplicas: 100
#   targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
