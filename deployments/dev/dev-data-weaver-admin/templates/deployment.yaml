apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.name }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "dev-data-weaver-admin.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "dev-data-weaver-admin.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "dev-data-weaver-admin.selectorLabels" . | nindent 8 }}
        group: {{ .Values.group }}
        env: {{ .Values.env }}
    spec:
      containers:
        - name: {{ .Values.name }}
          image: {{ .Values.image }}
          ports:
            - containerPort: {{ .Values.port }}
          resources:
            limits:
              cpu: 2000m 
              memory: 2000Mi
            requests:
              cpu: 1000m
              memory: 1000Mi