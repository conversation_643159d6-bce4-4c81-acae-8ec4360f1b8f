apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "dev-data-weaver-admin.fullname" . }}-test-connection"
  labels:
    {{- include "dev-data-weaver-admin.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "dev-data-weaver-admin.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
