apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.name }}
  namespace: {{ .Values.namespace }}
  group: {{ .Values.group }}
  labels:
    {{- include "dev-data-weaver-admin.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 80
      protocol: TCP
  selector:
    {{- include "dev-data-weaver-admin.selectorLabels" . | nindent 4 }}
