{"name": "data-weaver-admin-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@tailwindcss/vite": "^4.1.11", "antd": "^5.26.4", "axios": "^1.10.0", "dayjs": "^1.11.13", "form-data": "^4.0.4", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "sass": "^1.89.2", "tailwindcss": "^4.1.11", "vite": "^7.0.4"}}